/**
 * Cloudflare Turnstile 验证管理器
 * 专注于弹窗验证，简化代码结构
 */

// CF Turnstile 验证场景类型 (保持与原版本兼容)
export enum CF_TURNSTILE_TYPE {
  /** 无 */
  NONE = "",
  /** 手机号注册登录-获取验证码 */
  LOGIN_PHONE_GET_CODE = "SCENE_GET_CODE",
  /** 登录-提交 */
  LOGIN_SUBMIT = "SCENE_LOGIN",
  /** 忘记密码-获取验证码 */
  FORGET_PW_GET_CODE = "SCENE_FORGET_PW_GET_CODE",
  /** 忘记密码-提交 */
  FORGET_PW_SUBMIT = "SCENE_FORGET_PASSWORD",
  /** 首次设定登录密码 */
  FIRST_SET_LOGIN_PW = "SCENE_FIRST_PASSWORD",
  /** 首次设定支付密码 */
  FIRST_SET_PAY_PW = "SCENE_FIRST_PAY_PASSWORD",
  /** 修改登录密码-获取验证码 */
  MODIFY_LOGIN_PW_GET_CODE = "SCENE_MODIFY_LOGIN_PW_GET_CODE",
  /** 修改登录密码-提交 */
  MODIFY_LOGIN_PW_SUBMIT = "SCENE_CHANGE_PASSWORD",
  /** 修改支付密码-获取验证码 */
  MODIFY_PAY_PW_GET_CODE = "xxx",
  /** 修改支付密码-提交 */
  MODIFY_PAY_PW_SUBMIT = "SCENE_CHANGE_PAY_PASSWORD",
  /** 绑定提款账号-获取验证码 */
  BIND_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 绑定提款账号-提交 */
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_BIND_WITHDRAW_ACCOUNT",
  /** 修改提款账号-获取验证码 */
  MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 修改提款账号-提交 */
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_CHANGE_WITHDRAW_ACCOUNT",
  /** 提现-提交订单 */
  WITHDRAWAL_SUBMIT = "SCENE_WITHDRAW",
  /** 绑定手机号-获取验证码 */
  BIND_PHONE_GET_CODE = "xxx",
  /** 绑定手机号-提交 */
  BIND_PHONE_SUBMIT = "SCENE_BIND_PT_PHONE",
  /** 修改手机号-获取验证码 */
  MODIFY_PHONE_GET_CODE = "SCENE_MODIFY_PHONE_GET_CODE",
  /** 修改手机号-提交 */
  MODIFY_PHONE_SUBMIT = "SCENE_CHANGE_PT_PHONE",
  /** KYC 提交 */
  KYC_SUBMIT = "SCENE_SUB_KYC_INFO",
  /** 注册提交 */
  REGISTER_SUBMIT = "SCENE_REGISTER",
}

// Turnstile 验证结果接口
export interface TurnstileResult {
  success: boolean;
  token?: string;
  cfType: string;
  error?: string;
}

// Turnstile 配置接口
export interface TurnstileConfig {
  siteKey?: string;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact";
  language?: string;
  appearance?: "always" | "execute" | "interaction-only";
}

// 验证回调函数类型
export type VerifyCallback = (result: TurnstileResult | false) => void;

/**
 * Cloudflare Turnstile 管理器
 * 专注于弹窗验证，简化架构
 */
export class CloudflareMgr {
  private static _instance: CloudflareMgr;
  private isScriptLoaded = false;
  private loadingPromise: Promise<boolean> | null = null;

  // 单例模式
  static get instance(): CloudflareMgr {
    if (!this._instance) {
      this._instance = new CloudflareMgr();
    }
    return this._instance;
  }

  constructor() {
    this.initTurnstileScript();
  }

  /**
   * 初始化 Turnstile 脚本
   */
  private async initTurnstileScript(): Promise<boolean> {
    if (this.isScriptLoaded) {
      return true;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = new Promise((resolve) => {
      // 检查是否已经加载
      if (window.turnstile) {
        this.isScriptLoaded = true;
        resolve(true);
        return;
      }

      // 创建脚本标签
      const script = document.createElement("script");
      script.src = "https://challenges.cloudflare.com/turnstile/v0/api.js";
      script.async = true;
      script.defer = true;

      script.onload = () => {
        console.log("✅ Turnstile script loaded successfully");
        this.isScriptLoaded = true;
        resolve(true);
      };

      script.onerror = () => {
        console.error("❌ Failed to load Turnstile script");
        this.loadingPromise = null;
        resolve(false);
      };

      document.head.appendChild(script);
    });

    return this.loadingPromise;
  }

  /**
   * 根据环境获取 Site Key
   */
  public getSiteKey(): string {
    const env = import.meta.env.MODE;
    let siteKey: string;

    // 根据不同环境返回不同的 Site Key
    switch (env) {
      case "development":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_DEV || "0x4AAAAAABnByxp2v1NuTm7f";
        break;
      case "test":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_TEST || "0x4AAAAAABnByxp2v1NuTm7f";
        break;
      case "pre":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_PRE || "0x4AAAAAABpKiQV8_G7FJy6p";
        break;
      case "production":
        siteKey = import.meta.env.VITE_CF_SITE_KEY_PROD || "0x4AAAAAABpKiQV8_G7FJy6p";
        break;
      default:
        siteKey = "0x4AAAAAABnByxp2v1NuTm7f"; // 默认测试环境 Site Key
    }

    // 验证 Site Key 格式
    if (!this.validateSiteKey(siteKey)) {
      console.error("❌ Invalid Site Key format:", siteKey);
      throw new Error(`Invalid Site Key format: ${siteKey}`);
    }

    console.log(`🔑 Using site key for ${env}:`, siteKey);
    return siteKey;
  }

  /**
   * 验证 Site Key 格式
   */
  private validateSiteKey(siteKey: string): boolean {
    if (!siteKey || typeof siteKey !== "string") {
      return false;
    }
    // Cloudflare Site Key 格式: 0x4AAAAAABxxxxxxxxxxxxxxx
    const siteKeyPattern = /^0x4[A-Za-z0-9_-]{20,25}$/;
    return siteKeyPattern.test(siteKey);
  }

  /**
   * 渲染 Turnstile 验证组件
   * @param containerId 容器ID
   * @param cfType 验证类型
   * @param callback 回调函数
   * @param config 配置选项
   */
  public async renderTurnstile(
    containerId: string,
    cfType: CF_TURNSTILE_TYPE,
    callback: VerifyCallback,
    config?: TurnstileConfig
  ): Promise<string | null> {
    try {
      // 确保脚本已加载
      const scriptLoaded = await this.initTurnstileScript();
      if (!scriptLoaded) {
        throw new Error("Failed to load Turnstile script");
      }

      // 获取容器
      const container = document.getElementById(containerId);
      if (!container) {
        throw new Error(`Container with ID '${containerId}' not found`);
      }

      // 清空容器
      container.innerHTML = "";

      // 获取配置
      const siteKey = config?.siteKey || this.getSiteKey();

      // 构建 Turnstile 选项
      const turnstileOptions = {
        sitekey: siteKey,
        theme: config?.theme || "light",
        size: config?.size || "normal",
        language: config?.language || "en",
        appearance: config?.appearance || "always",
        callback: (token: string) => {
          console.log("🎉 Turnstile verification successful");
          const result: TurnstileResult = {
            success: true,
            token,
            cfType,
          };
          callback(result);
        },
        "error-callback": (error: string) => {
          console.error("❌ Turnstile verification failed:", error);
          const result: TurnstileResult = {
            success: false,
            cfType,
            error,
          };
          callback(result);
        },
        "expired-callback": () => {
          console.warn("⚠️ Turnstile verification expired");
          const result: TurnstileResult = {
            success: false,
            cfType,
            error: "expired",
          };
          callback(result);
        },
      };

      // 渲染 Turnstile 组件
      const widgetId = window.turnstile.render(container, turnstileOptions);
      console.log("✅ Turnstile widget rendered successfully, ID:", widgetId);

      return widgetId;
    } catch (error) {
      console.error("❌ Failed to render Turnstile:", error);
      callback(false);
      return null;
    }
  }

  /**
   * 重置 Turnstile 组件
   */
  public reset(widgetId: string): void {
    if (widgetId && window.turnstile) {
      window.turnstile.reset(widgetId);
    }
  }

  /**
   * 移除 Turnstile 组件
   */
  public remove(widgetId: string): void {
    if (widgetId && window.turnstile) {
      window.turnstile.remove(widgetId);
    }
  }

  /**
   * 获取当前 token (兼容旧版本API)
   */
  getToken(): string {
    // 新版本的 token 在验证回调中返回，这里返回空字符串
    return "";
  }

  /**
   * 清除当前 token (兼容旧版本API)
   */
  clearToken(): void {
    // 新版本的 token 不持久化，无需清除
  }
}

// 声明全局 turnstile 对象
declare global {
  interface Window {
    turnstile: {
      render: (container: HTMLElement, options: any) => string;
      reset: (widgetId: string) => void;
      remove: (widgetId: string) => void;
      getResponse: (widgetId: string) => string;
    };
  }
}
