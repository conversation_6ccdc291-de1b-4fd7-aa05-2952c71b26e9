# CloudflareVerifyDialog 组件

一个基于 Cloudflare Turnstile 的验证弹窗组件，提供简洁易用的验证界面。

## 特性

- 🚀 简单易用的弹窗验证界面
- 🎨 美观的 UI 设计，支持主题切换
- 🔧 灵活的配置选项
- 📱 响应式设计，支持移动端
- ⚡ 自动加载 Turnstile 脚本
- 🛡️ 完整的错误处理和重试机制
- 🎯 TypeScript 支持

## 基本用法

### 1. 组件方式使用

```vue
<template>
  <div>
    <van-button @click="showVerify = true">显示验证</van-button>

    <CloudflareVerifyDialog
      v-model="showVerify"
      :cf-type="CF_TURNSTILE_TYPE.LOGIN_SUBMIT"
      title="登录验证"
      description="请完成安全验证以继续登录"
      @success="handleSuccess"
      @error="handleError"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { CF_TURNSTILE_TYPE } from "@/utils/CloudflareMgr";
import CloudflareVerifyDialog from "@/components/CloudflareVerifyDialog/index.vue";

const showVerify = ref(false);

const handleSuccess = (result) => {
  console.log("验证成功:", result);
  // 处理验证成功逻辑
};

const handleError = (error) => {
  console.error("验证失败:", error);
  // 处理验证失败逻辑
};

const handleCancel = () => {
  console.log("用户取消验证");
  // 处理取消逻辑
};
</script>
```

### 2. API 方式使用

```typescript
import { showCloudflareVerify, CF_TURNSTILE_TYPE } from "@/utils/CloudflareVerifyAPI";

// 基本用法
const result = await showCloudflareVerify({
  cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
  title: "登录验证",
  description: "请完成安全验证以继续登录",
});

if (result.success) {
  console.log("验证成功，Token:", result.token);
} else {
  console.log("验证失败或取消");
}
```

### 3. 快捷方法

```typescript
import {
  verifyLogin,
  verifyRegister,
  verifyKYC,
  verifyWithdrawal,
} from "@/utils/CloudflareVerifyAPI";

// 登录验证
const loginResult = await verifyLogin();

// 注册验证
const registerResult = await verifyRegister();

// KYC验证
const kycResult = await verifyKYC();

// 提款验证
const withdrawalResult = await verifyWithdrawal();
```

## Props

| 参数             | 类型              | 默认值                  | 说明                                   |
| ---------------- | ----------------- | ----------------------- | -------------------------------------- |
| modelValue       | boolean           | false                   | 控制弹窗显示/隐藏                      |
| title            | string            | 'Security Verification' | 弹窗标题                               |
| description      | string            | 'Please complete...'    | 描述文本                               |
| cfType           | CF_TURNSTILE_TYPE | 必填                    | 验证类型                               |
| showCancelButton | boolean           | true                    | 是否显示取消按钮                       |
| siteKey          | string            | 自动获取                | 自定义 Site Key                        |
| theme            | string            | 'light'                 | 主题 (light/dark/auto)                 |
| size             | string            | 'normal'                | 尺寸 (normal/compact)                  |
| appearance       | string            | 'always'                | 外观 (always/execute/interaction-only) |
| autoCloseDelay   | number            | 2000                    | 验证成功后自动关闭延迟时间(ms)         |

## Events

| 事件名            | 参数            | 说明                |
| ----------------- | --------------- | ------------------- |
| update:modelValue | boolean         | 更新 v-model 绑定值 |
| success           | TurnstileResult | 验证成功事件        |
| error             | string          | 验证失败事件        |
| cancel            | -               | 取消事件            |

## 验证类型 (CF_TURNSTILE_TYPE)

```typescript
enum CF_TURNSTILE_TYPE {
  LOGIN_SUBMIT = "SCENE_LOGIN",
  REGISTER_SUBMIT = "SCENE_REGISTER",
  KYC_SUBMIT = "SCENE_SUB_KYC_INFO",
  WITHDRAWAL_SUBMIT = "SCENE_WITHDRAW",
  FORGET_PW_SUBMIT = "SCENE_FORGET_PASSWORD",
  MODIFY_LOGIN_PW_SUBMIT = "SCENE_CHANGE_PASSWORD",
  // ... 更多类型
}
```

## 返回结果

```typescript
interface VerifyResult {
  success: boolean; // 是否成功
  token?: string; // 验证 Token
  cfType: string; // 验证类型
  error?: string; // 错误信息
  cancelled?: boolean; // 是否被取消
}
```

## 高级用法

### 自定义配置

```typescript
const result = await showCloudflareVerify({
  cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
  title: "自定义验证",
  description: "这是一个自定义的验证弹窗",
  theme: "dark",
  size: "compact",
  appearance: "interaction-only",
  autoCloseDelay: 3000,
  showCancelButton: false,
});
```

### 错误处理

```typescript
try {
  const result = await verifyLogin();

  if (result.success) {
    // 验证成功
    console.log("Token:", result.token);
  } else if (result.cancelled) {
    // 用户取消
    console.log("用户取消了验证");
  } else {
    // 验证失败
    console.error("验证失败:", result.error);
  }
} catch (error) {
  // 系统错误
  console.error("系统错误:", error);
}
```

## 注意事项

1. **环境配置**: 确保在 `.env` 文件中正确配置了 Cloudflare Site Keys
2. **网络要求**: 需要能够访问 Cloudflare 的验证服务
3. **域名配置**: Site Key 需要与当前域名匹配
4. **脚本加载**: 组件会自动加载 Turnstile 脚本，无需手动引入

## 环境变量配置

```bash
# .env.development
VITE_CF_SITE_KEY_DEV=0x4AAAAAABnByxp2v1NuTm7f
VITE_CF_SITE_KEY_TEST=0x4AAAAAABnByxp2v1NuTm7f
VITE_CF_SITE_KEY_PRE=0x4AAAAAABpKiQV8_G7FJy6p
VITE_CF_SITE_KEY_PROD=0x4AAAAAABpKiQV8_G7FJy6p
```

## 故障排除

### 常见问题

1. **验证失败**: 检查 Site Key 是否正确配置
2. **脚本加载失败**: 检查网络连接和防火墙设置
3. **域名不匹配**: 确保 Site Key 配置了正确的域名
4. **样式问题**: 检查 CSS 是否正确加载

### 调试模式

在开发环境中，组件会输出详细的调试信息到控制台，帮助排查问题。
