/**
 * 登录相关类型定义
 */

// ==================== 登录表单类型 ====================

/**
 * 密码登录参数
 */
export interface PasswordLoginPayload {
  password: string;
  phone?: string;
}

/**
 * 验证码登录参数
 */
export interface CodeLoginPayload {
  verCode: string;
  phone: string;
}

/**
 * 验证结果 (支持 Geetest 和 Cloudflare)
 */
export interface GeetestResult {
  // Geetest 属性
  buds?: string;
  geetest_guard?: string;
  userInfo?: string;
  geetest_captcha?: string;
  // Cloudflare 属性
  "cf-token"?: string;
  "cf-scene"?: string;
  // 向后兼容的 Cloudflare 属性
  cf_token?: string;
  cf_type?: string;
}

/**
 * 登录模式
 */
export type LoginMode = "password" | "code";

/**
 * 登录类型
 */
export type LoginType = "phone_login_code" | "password_login" | "phone_code_login";

// ==================== Store 状态类型 ====================

/**
 * 登录 Store 状态
 */
export interface LoginStoreState {
  /** 用户手机号 */
  userPhone: string;
  /** 当前登录模式 */
  currentLoginMode: LoginMode;
  /** 是否处于验证码输入模式 */
  isCodeInputMode: boolean;
  /** 是否同意隐私协议 */
  isPrivacyAgreed: boolean;
  /** 隐私政策弹窗是否可见 */
  isPrivacyDialogVisible: boolean;
  /** 是否正在登录 */
  isLoggingIn: boolean;
}

// ==================== 登录参数类型 ====================

/**
 * 密码登录参数
 */
export interface PasswordLoginParams {
  login_type: string;
  phone: string;
  password: string;
  buds?: string;
  geetest_guard?: string;
  userInfo?: string;
  geetest_captcha?: string;
}

/**
 * 验证码登录参数
 */
export interface CodeLoginParams {
  login_type: string;
  phone: string;
  verifyCode: string;
  buds?: string;
  geetest_guard?: string;
  userInfo?: string;
  geetest_captcha?: string;
}

// ==================== 回调函数类型 ====================

/**
 * Geetest 验证回调
 */
export type GeetestCallback = (result: GeetestResult) => Promise<void>;

/**
 * 登录成功回调
 */
export type LoginSuccessCallback = () => void;

/**
 * 登录失败回调
 */
export type LoginErrorCallback = (error: Error) => void;
