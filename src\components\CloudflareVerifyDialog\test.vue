<div id="dynamic-form-container"></div>

<script src="https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit"></script>

<script>
class TurnstileManager {
  constructor() {
    this.widgets = new Map();
  }
  createWidget(containerId, config) {
    // Wait for Turnstile to be ready
    turnstile.ready(() => {
      const widgetId = turnstile.render(containerId, {
        sitekey: config.sitekey,
        theme: config.theme || "auto",
        size: config.size || "normal",
        callback: (token) => {
          console.log(`Widget ${widgetId} completed:`, token);
          if (config.onSuccess) config.onSuccess(token, widgetId);
        },
        "error-callback": (error) => {
          console.error(`Widget ${widgetId} error:`, error);
          if (config.onError) config.onError(error, widgetId);
        },
      });

      this.widgets.set(containerId, widgetId);
      return widgetId;
    });
  }
  removeWidget(containerId) {
    const widgetId = this.widgets.get(containerId);
    if (widgetId) {
      turnstile.remove(widgetId);
      this.widgets.delete(containerId);
    }
  }
  resetWidget(containerId) {
    const widgetId = this.widgets.get(containerId);
    if (widgetId) {
      turnstile.reset(widgetId);
    }
  }
}

// Usage
const manager = new TurnstileManager();

// Create a widget when user clicks a button
document.getElementById("show-form-btn").addEventListener("click", () => {
  document.getElementById("dynamic-form-container").innerHTML = `
        <form>
            <input type="email" placeholder="Email" />
            <div id="turnstile-widget"></div>
            <button type="submit">Submit</button>
        </form>
    `;
  manager.createWidget("#turnstile-widget", {
    sitekey: "<YOUR-SITE-KEY>",
    theme: "dark",
    onSuccess: (token) => {
      // Handle successful verification
      console.log("Form ready for submission");
    },
  });
});
</script>
