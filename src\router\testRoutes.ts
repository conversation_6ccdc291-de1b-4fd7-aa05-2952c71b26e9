/**
 * 测试路由配置
 */

import type { RouteRecordRaw } from "vue-router";

export const testRoutes: RouteRecordRaw[] = [
  // 测试页面导航
  {
    path: "/test",
    name: "TestIndex",
    component: () =>
      import(
        /* webpackChunkName: "views/test/index", webpackPrefetch: true */ "@/views/test/index.vue"
      ),
    meta: { requiresAuth: false, title: "测试页面导航" },
  },
  // 金币动画演示
  {
    path: "/test/coin",
    name: "CoinAnimationDemo",
    component: () =>
      import(
        /* webpackChunkName: "views/test/CoinAnimationDemo", webpackPrefetch: true */ "@/views/test/CoinAnimationDemo.vue"
      ),
    meta: { requiresAuth: false, title: "金币动画演示" },
  },
  // 奖励钱包返回逻辑测试
  {
    path: "/test/bonus-wallet-test",
    name: "BonusWalletTest",
    component: () =>
      import(
        /* webpackChunkName: "views/test/bonus-wallet-test", webpackPrefetch: true */ "@/views/test/bonus-wallet-test.vue"
      ),
    meta: { requiresAuth: false, title: "奖励钱包返回逻辑测试" },
  },

  {
    path: "/test/copylink-test",
    name: "CopyLinkTest",
    component: () =>
      import(
        /* webpackChunkName: "views/test/bonus-wallet-test", webpackPrefetch: true */ "@/views/test/CopyLinkTest.vue"
      ),
    meta: { requiresAuth: false, title: "奖励钱包返回逻辑测试" },
  },
  // WalletRewardBtn 图片序列动画测试
  {
    path: "/test/wallet-reward-btn",
    name: "WalletRewardBtnTest",
    component: () =>
      import(
        /* webpackChunkName: "views/test/WalletRewardBtnTest", webpackPrefetch: true */ "@/views/test/WalletRewardBtnTest.vue"
      ),
    meta: { requiresAuth: false, title: "钱包奖励按钮测试" },
  },
  // PublicActivityBonusTip 组件测试
  {
    path: "/test/public-activity-bonus-tip",
    name: "PublicActivityBonusTipTest",
    component: () =>
      import(
        /* webpackChunkName: "views/test/PublicActivityBonusTipTest", webpackPrefetch: true */ "@/views/test/PublicActivityBonusTipTest.vue"
      ),
    meta: { requiresAuth: false, title: "通用奖励弹窗测试" },
  },
  // GradientButton 组件测试
  {
    path: "/test/gradient-button",
    name: "GradientButtonTest",
    component: () =>
      import(
        /* webpackChunkName: "views/test/GradientButtonTest", webpackPrefetch: true */ "@/views/test/GradientButtonTest.vue"
      ),
    meta: { requiresAuth: false, title: "渐变按钮组件测试" },
  },
  {
    path: "/test/NewsTest",
    name: "NewsTest",
    component: () =>
      import(
        /* webpackChunkName: "views/test/NewsTest", webpackPrefetch: true */ "@/views/test/NewsTest.vue"
      ),
    meta: { requiresAuth: false, title: "新闻组件测试" },
  },
  // Cloudflare 验证测试页面（集成所有功能）
  {
    path: "/test/cloudflare",
    name: "CloudflareTest",
    component: () =>
      import(
        /* webpackChunkName: "views/test/CloudflareTest", webpackPrefetch: true */ "@/views/test/CloudflareTest.vue"
      ),
    meta: { requiresAuth: false, title: "Cloudflare 验证测试" },
  },
  // Cloudflare 验证使用示例
  {
    path: "/test/cloudflare-examples",
    name: "CloudflareExample",
    component: () =>
      import(
        /* webpackChunkName: "views/examples/CloudflareExample", webpackPrefetch: true */ "@/views/examples/CloudflareExample.vue"
      ),
    meta: { requiresAuth: false, title: "Cloudflare 验证示例" },
  },
  // Cloudflare Turnstile 组件测试页面
  {
    path: "/test/turnstile-test",
    name: "TurnstileTest",
    component: () =>
      import(
        /* webpackChunkName: "components/CloudflareVerifyDialog/test", webpackPrefetch: true */ "@/components/CloudflareVerifyDialog/test.vue"
      ),
    meta: { requiresAuth: false, title: "Turnstile 组件测试" },
  },
  // Cloudflare Turnstile 简单示例
  {
    path: "/test/turnstile-example",
    name: "TurnstileExample",
    component: () =>
      import(
        /* webpackChunkName: "components/CloudflareVerifyDialog/TurnstileExample", webpackPrefetch: true */ "@/components/CloudflareVerifyDialog/TurnstileExample.vue"
      ),
    meta: { requiresAuth: false, title: "Turnstile 简单示例" },
  },
];
